#!/usr/bin/env python3
"""
WebSocket连接测试脚本
用于测试修复后的WebSocket连接是否正常工作
"""

import asyncio
import websockets
import json
import logging

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

async def test_websocket():
    """测试WebSocket连接和消息处理"""
    uri = "ws://localhost:8000/api/v1/ws/chat"

    logger.info(f"尝试连接到: {uri}")

    try:
        async with websockets.connect(uri) as websocket:
            logger.info("WebSocket连接成功")
            
            # 发送测试消息
            test_message = {
                "yu_input": "你好",
                "session_id": "test_session"
            }
            
            await websocket.send(json.dumps(test_message))
            logger.info(f"发送消息: {test_message}")
            
            # 接收响应
            message_count = 0
            while True:
                try:
                    response = await asyncio.wait_for(websocket.recv(), timeout=10.0)
                    message_count += 1
                    
                    # 解析响应
                    try:
                        data = json.loads(response)
                        event_type = data.get("type")
                        payload = data.get("payload", {})
                        
                        logger.info(f"消息 {message_count}: {event_type}")
                        
                        if event_type == "stream_start":
                            logger.info("流开始")
                        elif event_type == "text_chunk":
                            chunk = payload.get("chunk", "")
                            logger.info(f"文本块: '{chunk[:50]}...'")
                        elif event_type == "stream_end":
                            logger.info("流结束")
                            break
                        elif event_type == "error":
                            logger.error(f"错误: {payload.get('message')}")
                            break
                            
                    except json.JSONDecodeError:
                        logger.warning(f"非JSON消息: {response[:100]}...")
                        
                except asyncio.TimeoutError:
                    logger.warning("接收消息超时")
                    break
                    
            logger.info(f"总共接收到 {message_count} 条消息")
            
    except Exception as e:
        logger.error(f"WebSocket连接失败: {e}")

if __name__ == "__main__":
    asyncio.run(test_websocket())
