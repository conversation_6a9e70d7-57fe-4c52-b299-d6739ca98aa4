# xi_system/api/protocol_utils.py

"""
WebSocket Protocol Utilities for Xi ContextOS

This module provides utilities for creating standardized WebSocket messages
that conform to the Xi System WebSocket Protocol. All WebSocket communication
between frontend and backend must use these structured JSON messages.

Protocol Structure:
{
    "type": "event_type_string",
    "payload": { ... },
    "metadata": {
        "message_id": "unique_message_id_for_this_response_stream",
        "timestamp": "iso_8601_timestamp"
    }
}

Event Types:
- stream_start: Beginning of a new response stream
- tool_call: Backend is calling a tool
- text_chunk: LLM is generating text content
- stream_end: Complete response stream finished
- error: Error occurred during processing
- background_task_started: Background task initiated (V0.2+)
- background_task_progress: Background task progress update (V0.2+)
- background_task_completed: Background task completed (V0.2+)

Usage:
    from .protocol_utils import create_protocol_message
    
    # Create a text chunk message
    message = create_protocol_message(
        "text_chunk",
        {"chunk": "Hello, world!"},
        message_id="response_123"
    )
    
    # Send via WebSocket
    await websocket.send_text(message)
"""

import json
import uuid
from datetime import datetime, timezone
from typing import Any, Dict, Optional

def create_protocol_message(
    event_type: str,
    payload: Dict[str, Any],
    message_id: Optional[str] = None
) -> str:
    """
    创建一个符合标准协议的JSON消息字符串。

    Args:
        event_type: 事件类型 (e.g., 'text_chunk', 'tool_call', 'stream_start').
        payload: 事件的载荷，包含与该事件类型相关的具体数据.
        message_id: 响应流的唯一ID. 如果未提供，将生成一个新的.

    Returns:
        序列化后的JSON字符串，可直接通过WebSocket发送.
        
    Example:
        >>> create_protocol_message("text_chunk", {"chunk": "Hello"}, "msg_123")
        '{"type": "text_chunk", "payload": {"chunk": "Hello"}, "metadata": {"message_id": "msg_123", "timestamp": "2024-01-01T12:00:00+00:00"}}'
    """
    message = {
        "type": event_type,
        "payload": payload,
        "metadata": {
            "message_id": message_id or str(uuid.uuid4()),
            "timestamp": datetime.now(timezone.utc).isoformat()
        }
    }
    return json.dumps(message, ensure_ascii=False)


def parse_protocol_message(message_str: str) -> Dict[str, Any]:
    """
    解析协议消息字符串为字典对象。

    Args:
        message_str: JSON格式的协议消息字符串

    Returns:
        解析后的消息字典

    Raises:
        json.JSONDecodeError: 当消息不是有效JSON时
        KeyError: 当消息缺少必需字段时
        
    Example:
        >>> msg = '{"type": "text_chunk", "payload": {"chunk": "Hello"}, "metadata": {"message_id": "msg_123", "timestamp": "2024-01-01T12:00:00+00:00"}}'
        >>> parsed = parse_protocol_message(msg)
        >>> parsed["type"]
        'text_chunk'
    """
    try:
        message = json.loads(message_str)
        
        # 验证必需字段
        required_fields = ["type", "payload", "metadata"]
        for field in required_fields:
            if field not in message:
                raise KeyError(f"Missing required field: {field}")
        
        # 验证metadata必需字段
        metadata_required = ["message_id", "timestamp"]
        for field in metadata_required:
            if field not in message["metadata"]:
                raise KeyError(f"Missing required metadata field: {field}")
        
        return message
        
    except json.JSONDecodeError as e:
        # 重新抛出原始异常，添加更多上下文信息
        raise json.JSONDecodeError(
            f"Invalid JSON in protocol message: {str(e)}",
            e.doc,
            e.pos
        ) from e


def create_stream_start_message(session_id: str, input_message_id: str, message_id: str) -> str:
    """
    创建stream_start事件消息。

    Args:
        session_id: 当前会话ID
        input_message_id: 触发此响应流的用户消息ID
        message_id: 此响应流的唯一ID

    Returns:
        JSON格式的stream_start消息
    """
    payload = {
        "session_id": session_id,
        "input_message_id": input_message_id
    }
    return create_protocol_message("stream_start", payload, message_id)


def create_tool_call_message(tool_name: str, arguments: Dict[str, Any], message_id: str) -> str:
    """
    创建tool_call事件消息。

    Args:
        tool_name: 被调用的工具名称
        arguments: 工具调用参数
        message_id: 此响应流的唯一ID

    Returns:
        JSON格式的tool_call消息
    """
    payload = {
        "tool_name": tool_name,
        "arguments": arguments
    }
    return create_protocol_message("tool_call", payload, message_id)


def create_text_chunk_message(chunk: str, message_id: str) -> str:
    """
    创建text_chunk事件消息。

    Args:
        chunk: 文本块内容
        message_id: 此响应流的唯一ID

    Returns:
        JSON格式的text_chunk消息
    """
    payload = {"chunk": chunk}
    return create_protocol_message("text_chunk", payload, message_id)


def create_stream_end_message(message_id: str, final_content: Optional[str] = None) -> str:
    """
    创建stream_end事件消息。

    Args:
        message_id: 此响应流的唯一ID
        final_content: 可选的完整最终消息内容

    Returns:
        JSON格式的stream_end消息
    """
    payload = {}
    if final_content is not None:
        payload["final_content"] = final_content
    return create_protocol_message("stream_end", payload, message_id)


def create_error_message(error_message: str, error_code: Optional[int] = None, message_id: Optional[str] = None) -> str:
    """
    创建error事件消息。

    Args:
        error_message: 错误描述信息
        error_code: 可选的错误码
        message_id: 可选的响应流ID（如果错误发生在特定流中）

    Returns:
        JSON格式的error消息
    """
    payload = {"message": error_message}
    if error_code is not None:
        payload["code"] = error_code
    return create_protocol_message("error", payload, message_id)


def create_background_task_started_message(task_id: str, task_type: str, description: str) -> str:
    """
    创建background_task_started事件消息（V0.2预留）。

    Args:
        task_id: 任务唯一ID
        task_type: 任务类型（如"reflection"）
        description: 任务描述

    Returns:
        JSON格式的background_task_started消息
    """
    payload = {
        "task_id": task_id,
        "task_type": task_type,
        "description": description
    }
    return create_protocol_message("background_task_started", payload)


def create_background_task_progress_message(task_id: str, progress: float, message: str) -> str:
    """
    创建background_task_progress事件消息（V0.2预留）。

    Args:
        task_id: 任务唯一ID
        progress: 进度值（0.0-1.0）
        message: 进度描述信息

    Returns:
        JSON格式的background_task_progress消息
    """
    payload = {
        "task_id": task_id,
        "progress": progress,
        "message": message
    }
    return create_protocol_message("background_task_progress", payload)


def create_background_task_completed_message(task_id: str, result: Dict[str, Any]) -> str:
    """
    创建background_task_completed事件消息（V0.2预留）。

    Args:
        task_id: 任务唯一ID
        result: 任务执行结果

    Returns:
        JSON格式的background_task_completed消息
    """
    payload = {
        "task_id": task_id,
        "result": result
    }
    return create_protocol_message("background_task_completed", payload)
