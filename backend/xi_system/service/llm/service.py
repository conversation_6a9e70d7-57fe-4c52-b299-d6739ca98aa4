# xi_system/service/llm/service.py

"""
LLM服务 - 多提供商统一调度器

统一的LLM服务，根据配置管理和调度不同的LLM提供商。
支持策略模式和工厂模式，提供灵活的提供商切换能力。

设计原则：
- 提供商解耦：核心服务与具体LLM实现完全分离
- 工厂模式：根据配置动态创建提供商实例
- 统一接口：所有提供商通过相同接口访问
- 角色映射：支持内部个性化角色名转换
- 错误处理：统一的错误处理和重试机制

支持的提供商：
- Google: 通过OpenAI兼容接口访问Gemini模型
- 未来可扩展：OpenAI、Anthropic等其他提供商
"""

import logging
from typing import List, Dict, Any, AsyncGenerator
from ...service.container import ServiceInterface
from ...service.config import ConfigService
from .providers.base import LLMProvider
from .providers.google import GoogleLLMProvider
from .providers.mock import MockLLMProvider
# 未来可以从这里导入其他提供商

logger = logging.getLogger(__name__)


class LLMService(ServiceInterface):
    """
    统一的LLM服务，根据配置管理和调度不同的LLM提供商。
    """

    def __init__(self, config_service: ConfigService):
        """
        初始化LLM服务

        Args:
            config_service: 配置服务实例
        """
        self.config = config_service
        self.provider: LLMProvider = None

        # 角色映射：内部个性化命名 → 外部标准格式
        self.role_mapping = {
            "xi_system": "system",
            "yu": "user",
            "xi": "assistant",
            "tool": "tool"
        }

    def initialize(self) -> None:
        """初始化LLM服务"""
        # 检查是否处于测试模式
        if self.config.get_bool('system.test_mode', False):
            logger.warning("!!! SYSTEM IS RUNNING IN TEST MODE !!!")
            logger.warning("LLM API calls will be mocked.")
            self.provider = MockLLMProvider(self.config)
            self.provider.initialize()
            logger.info("LLMService initialized with MockProvider.")
            return  # 提前返回，不执行后续的真实Provider初始化

        provider_name = self.config.get('llm.provider', 'google').lower()
        logger.info(f"Initializing LLMService with provider: {provider_name}")

        if provider_name == 'google':
            api_key = self.config.get_str('llm.providers.google.api_key')
            base_url = self.config.get_str('llm.providers.google.base_url')
            model = self.config.get_str('llm.providers.google.model')
            timeout = self.config.get_int('llm.request_timeout')
            self.provider = GoogleLLMProvider(
                api_key=api_key,
                base_url=base_url,
                model=model,
                timeout=timeout
            )
        # elif provider_name == 'openai':
        #     # 未来OpenAI的实现
        #     pass
        else:
            raise ValueError(f"Unsupported LLM provider: {provider_name}")

        self.provider.initialize()
        logger.info("LLMService initialized successfully.")

    def cleanup(self) -> None:
        """清理LLM服务"""
        self.provider = None
        logger.info("LLMService cleaned up.")

    def _apply_role_mapping(self, messages: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        应用角色映射

        将内部个性化角色名转换为外部API格式

        Args:
            messages: 消息列表

        Returns:
            映射后的消息列表
        """
        mapped_messages = []

        for message in messages:
            mapped_message = message.copy()
            role = message.get("role", "")
            mapped_message["role"] = self.role_mapping.get(role, role)
            mapped_messages.append(mapped_message)

        return mapped_messages

    def sync_chat(self, messages: List[Dict[str, Any]], **kwargs) -> str:
        """
        同步对话

        Args:
            messages: 消息列表
            **kwargs: 额外的LLM参数

        Returns:
            完整的响应内容
        """
        if not self.provider:
            raise RuntimeError("LLMService not initialized.")

        mapped_messages = self._apply_role_mapping(messages)
        return self.provider.sync_chat(mapped_messages, **kwargs)

    async def stream_chat(self, messages: List[Dict[str, Any]], **kwargs) -> AsyncGenerator[str, None]:
        """
        流式对话

        Args:
            messages: 消息列表
            **kwargs: 额外的LLM参数

        Yields:
            流式响应内容
        """
        if not self.provider:
            raise RuntimeError("LLMService not initialized.")

        mapped_messages = self._apply_role_mapping(messages)
        async for chunk in self.provider.stream_chat(mapped_messages, **kwargs):
            yield chunk

    def get_client(self) -> Any:
        """获取LLM客户端实例"""
        if not self.provider:
            raise RuntimeError("LLMService not initialized.")
        return self.provider.get_client()

    def get_config(self) -> Dict[str, Any]:
        """获取LLM配置"""
        provider_name = self.config.get('llm.provider', 'google').lower()
        return {
            'model': self.config.get_str(f'llm.providers.{provider_name}.model'),
            'reasoning_effort': self.config.get_str('llm_reasoning_effort', 'none'),
            'timeout': self.config.get_int('llm.request_timeout')
        }

    def health_check(self) -> Dict[str, Any]:
        """
        LLM服务健康检查

        Returns:
            健康检查结果
        """
        if not self.provider:
            return {
                'status': 'error',
                'message': 'LLMService not initialized',
                'connected': False
            }

        try:
            # 检查是否为测试模式
            if self.config.get_bool('system.test_mode', False):
                return {
                    'status': 'healthy',
                    'connected': True,
                    'provider': 'mock',
                    'model': 'mock-llm',
                    'test_mode': True,
                    'message': 'Running in test mode with MockLLMProvider'
                }

            # 执行快速连接测试
            test_messages = [
                {"role": "system", "content": "You are a helpful assistant."},
                {"role": "user", "content": "Hello"}
            ]

            # 使用提供商进行测试
            response = self.provider.sync_chat(test_messages, max_tokens=10)

            return {
                'status': 'healthy',
                'connected': True,
                'provider': self.config.get('llm.provider', 'google'),
                'model': self.config.get_str('llm.providers.google.model'),
                'base_url': self.config.get_str('llm.providers.google.base_url'),
                'timeout': self.config.get_int('llm.request_timeout')
            }

        except Exception as e:
            return {
                'status': 'error',
                'connected': False,
                'error': str(e)
            }

    def get_model_info(self) -> Dict[str, Any]:
        """获取当前模型信息"""
        provider_name = self.config.get('llm.provider', 'google').lower()
        return {
            'provider': provider_name,
            'model': self.config.get_str(f'llm.providers.{provider_name}.model'),
            'base_url': self.config.get_str(f'llm.providers.{provider_name}.base_url'),
            'timeout': self.config.get_int('llm.request_timeout'),
            'role_mapping': self.role_mapping
        }

    def update_role_mapping(self, mapping: Dict[str, str]) -> None:
        """
        更新角色映射

        Args:
            mapping: 新的角色映射字典
        """
        self.role_mapping.update(mapping)
        logger.info(f"Role mapping updated: {self.role_mapping}")

    def format_tool_call_message(self, tool_calls: List[Dict[str, Any]]) -> str:
        """
        格式化工具调用消息

        Args:
            tool_calls: 工具调用列表

        Returns:
            格式化的消息内容
        """
        if not tool_calls:
            return ""

        formatted_calls = []
        for call in tool_calls:
            tool_name = call.get('function', {}).get('name', 'unknown')
            formatted_calls.append(f"[正在使用我的能力...🛠️] {tool_name}")

        return "\n".join(formatted_calls)
