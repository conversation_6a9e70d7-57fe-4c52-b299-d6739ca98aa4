// src/features/chat/index.tsx
import { useRef, useCallback } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { useChat } from './hooks/useChat';
import { useAutoScroll } from '@/hooks/useAutoScroll';
import { useWelcomeTransition } from './hooks/useWelcomeTransition';
import { LogStream } from './components/LogStream';
import { WelcomeScreen } from './components/WelcomeScreen';
import { AnimatedFooter } from './components/AnimatedFooter';
import { ScrollToBottomButton } from './components/ScrollToBottomButton';
import { ConnectionStatus } from '@/components/ui/ConnectionStatus';

export const ChatView = () => {
  const { messages, sendMessage, isThinking } = useChat();
  const footerRef = useRef<HTMLDivElement>(null);

  // 使用欢迎页过渡动画Hook - 基于消息数量
  const { isWelcomeScreen, footerMotionProps } = useWelcomeTransition(messages.length);

  // 使用自动滚动hook - 将 messages 和 isThinking 作为依赖项传递
  const {
    scrollContainerRef,
    showScrollButton,
    scrollToBottom,
  } = useAutoScroll([messages, isThinking], { threshold: 100 });

  // 发送消息时的滚动逻辑
  const handleSendMessage = useCallback((message: string) => {
    sendMessage(message);
    // 关键：发送消息时，强制启用自动滚动并立即滚动到底部
    scrollToBottom('auto');
  }, [sendMessage, scrollToBottom]);

  // 使用AnimatePresence管理WelcomeScreen和对话界面的过渡
  return (
    <div className="h-screen bg-background text-foreground font-sans relative overflow-hidden">
      <AnimatePresence>
        {isWelcomeScreen ? (
          <WelcomeScreen
            key="welcome" // key是AnimatePresence正确工作的关键
            onSendMessage={handleSendMessage}
            disabled={isThinking}
          />
        ) : (
          <motion.div
            key="chat"
            className="h-full"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ duration: 0.5 }}
          >
            {/* 滚动区域 - 只包含消息内容 */}
            <div
              ref={scrollContainerRef}
              className="h-full pb-32 overflow-y-auto"
              style={{
                scrollBehavior: 'auto' // 移除CSS的smooth，由JS控制
              }}
            >
              <div className="flex justify-center">
                <LogStream messages={messages} isThinking={isThinking} />
              </div>
            </div>

            {/* 固定在底部的输入框 - 完全独立 */}
            <div
              ref={footerRef}
              className="absolute bottom-0 left-0 right-0"
            >
              <AnimatedFooter
                onSendMessage={handleSendMessage}
                disabled={isThinking}
                motionProps={footerMotionProps}
              />
            </div>

            {/* 滚动到底部按钮 */}
            <ScrollToBottomButton
              show={showScrollButton}
              onClick={() => scrollToBottom('smooth')}
            />

            {/* WebSocket连接状态指示器 */}
            <ConnectionStatus />
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
};