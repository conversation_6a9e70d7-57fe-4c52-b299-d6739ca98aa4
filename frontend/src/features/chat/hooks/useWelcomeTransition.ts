// src/features/chat/hooks/useWelcomeTransition.ts
// 欢迎页过渡动画Hook - 管理从欢迎状态到对话状态的过渡逻辑
import { useMemo } from 'react';

interface WelcomeTransition {
  /** 是否应显示欢迎屏幕 */
  isWelcomeScreen: boolean;
  /** 传递给 motion.footer 的动画属性 */
  footerMotionProps: {
    layout: boolean;
    initial: {
      y: string | number;
      opacity: number;
    };
    animate: {
      y: number;
    };
    transition: {
      duration: number;
      ease: number[];
      type: string;
    };
  };
}

/**
 * useWelcomeTransition Hook - 基于消息数量的过渡动画管理
 *
 * 核心逻辑：
 * 1. 动画的触发精确依赖于消息数量，而不是hasStarted状态
 * 2. 动画只在messages.length从0变为1的瞬间被激活
 * 3. ChatInput组件从屏幕中央平滑移动到底部固定位置
 * 4. 使用优雅的缓动函数，模拟"进入工作状态"的感觉
 *
 * 解决的问题：
 * - 确保动画时机与用户发送第一条消息完全同步
 * - 避免输入框闪烁和视觉脱节
 * - 提供无缝的场景过渡体验
 *
 * @param messagesCount 当前消息数量
 * @returns 包含欢迎屏幕状态和footer动画属性的对象
 */
export const useWelcomeTransition = (messagesCount: number): WelcomeTransition => {
  // isWelcomeScreen 的判断逻辑基于消息数量
  const isWelcomeScreen = messagesCount === 0;

  // 计算footer动画属性 - 动画只在第一条消息出现时触发
  const footerMotionProps = useMemo(() => {
    // 动画只在第一条消息（用户发送的）出现时触发
    const shouldAnimate = messagesCount === 1;

    return {
      layout: true,
      initial: {
        y: shouldAnimate ? "calc(-50vh + 10rem)" : 0, // 初始位置更精确
        opacity: 1, // 初始透明度为1，避免闪烁
      },
      animate: {
        y: 0,
      },
      transition: {
        duration: 0.8, // 动画时长
        ease: [0.22, 1, 0.36, 1] as number[],
        type: "tween" as const,
      },
    };
  }, [messagesCount]);

  return {
    isWelcomeScreen,
    footerMotionProps
  };
};
