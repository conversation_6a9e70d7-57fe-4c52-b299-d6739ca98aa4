// src/hooks/useAutoScroll.ts
// 通用自动滚动管理Hook - 处理滚动容器的智能滚动逻辑
import { useRef, useCallback, useState, useEffect } from 'react';

interface UseAutoScrollOptions {
  /**
   * 判断是否接近底部的阈值（像素）
   * 当距离底部小于此值时，认为用户在底部
   */
  threshold?: number;
  /**
   * 是否启用调试日志
   */
  debug?: boolean;
}

interface UseAutoScrollReturn {
  /** 滚动容器的ref */
  scrollContainerRef: React.RefObject<HTMLDivElement | null>;
  /** 是否显示滚动到底部按钮 */
  showScrollButton: boolean;
  /** 滚动到底部的函数 */
  scrollToBottom: (behavior?: ScrollBehavior) => void;
}

/**
 * useAutoScroll Hook - 智能滚动行为管理
 *
 * 核心逻辑：
 * 1. 引入isAutoScrollingEnabled状态来跟踪是否应启用自动跟随
 * 2. 场景A: 用户发送消息时，立即滚动到底部
 * 3. 场景B: AI流式输出且用户在底部时，自动跟随新内容
 * 4. 场景C: AI流式输出但用户已向上滚动时，不自动滚动
 * 5. 场景D: 用户从上方返回底部时，重新启用自动跟随
 *
 * @param dependencies 依赖项数组，当内容更新时触发检查
 * @param options 配置选项
 */
export const useAutoScroll = (
  dependencies: any[] = [], // 依赖项数组，当内容更新时触发检查
  options: UseAutoScrollOptions = {}
): UseAutoScrollReturn => {
  const { threshold = 50 } = options;

  const scrollContainerRef = useRef<HTMLDivElement>(null);
  const [showScrollButton, setShowScrollButton] = useState(false);

  // 关键状态：使用ref来避免不必要的重渲染
  const isAutoScrollingEnabled = useRef(true);
  const lastScrollHeight = useRef(0);

  const scrollToBottom = useCallback((behavior: ScrollBehavior = 'auto') => {
    const container = scrollContainerRef.current;
    if (!container) return;

    // 使用 requestAnimationFrame 确保在下一次绘制前滚动
    requestAnimationFrame(() => {
      container.scrollTo({ top: container.scrollHeight, behavior });
    });

    // 如果是用户主动点击滚动到底部，则重新启用自动滚动
    if (behavior === 'smooth') {
      isAutoScrollingEnabled.current = true;
    }
  }, []);

  const handleScroll = useCallback(() => {
    const container = scrollContainerRef.current;
    if (!container) return;

    const { scrollTop, scrollHeight, clientHeight } = container;
    const distanceFromBottom = scrollHeight - scrollTop - clientHeight;

    // 如果是程序触发的滚动（内容增加），则不改变自动滚动状态
    if (scrollHeight > lastScrollHeight.current && isAutoScrollingEnabled.current) {
      lastScrollHeight.current = scrollHeight;
      return;
    }

    const isAtBottom = distanceFromBottom < threshold;
    isAutoScrollingEnabled.current = isAtBottom;
    setShowScrollButton(!isAtBottom);
  }, [threshold]);

  useEffect(() => {
    const container = scrollContainerRef.current;
    if (!container) return;

    // 当内容更新时（由依赖项数组触发）
    if (isAutoScrollingEnabled.current) {
      scrollToBottom('auto'); // 立即滚动，非平滑
    }
    lastScrollHeight.current = container.scrollHeight;

    // 显示/隐藏按钮的逻辑
    const { scrollTop, scrollHeight, clientHeight } = container;
    setShowScrollButton(scrollHeight - scrollTop - clientHeight > threshold);

  }, [dependencies, scrollToBottom, threshold]);

  useEffect(() => {
    const container = scrollContainerRef.current;
    if (!container) return;
    container.addEventListener('scroll', handleScroll, { passive: true });
    return () => container.removeEventListener('scroll', handleScroll);
  }, [handleScroll]);

  return {
    scrollContainerRef,
    showScrollButton,
    scrollToBottom,
  };
};
